import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../core/constants/app_colors.dart';
import '../core/navigation/app_routes.dart';

class MainWrapper extends StatelessWidget {
  final Widget child;

  const MainWrapper({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: _buildNavigationButtons(context),
    );
  }

  Widget _buildNavigationButtons(BuildContext context) {
    final String location = GoRouterState.of(context).uri.path;

    final bool isHomeSelected = location.startsWith(AppRoutes.home);
    final bool isProfileSelected = location.startsWith(AppRoutes.profile);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Safe<PERSON><PERSON>(
        child: Row(
          children: [
            // Anasayfa Button
            Expanded(
              child: _buildNavigationButton(
                context: context,
                label: 'Anasayfa',
                icon: Icons.home,
                isSelected: isHomeSelected,
                onTap: () => context.go(AppRoutes.home),
              ),
            ),
            const SizedBox(width: 12),
            // Profil Button
            Expanded(
              child: _buildNavigationButton(
                context: context,
                label: 'Profil',
                icon: Icons.person,
                isSelected: isProfileSelected,
                onTap: () => context.go(AppRoutes.profile),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButton({
    required BuildContext context,
    required String label,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.onSurfaceVariant,
            width: 1.5,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : AppColors.onSurfaceVariant,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : AppColors.onSurfaceVariant,
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
