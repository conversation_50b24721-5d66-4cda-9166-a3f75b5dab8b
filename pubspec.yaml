name: sinflix
description: "SinFlix - Premium Movie Streaming App"

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.10+1

  # State Management
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5

  # Immutable Data Classes
  freezed_annotation: ^2.4.4

  # Networking
  dio: ^5.7.0

  # Dependency Injection
  get_it: ^8.0.2

  # Navigation
  go_router: ^14.6.2

  # Storage
  flutter_secure_storage: ^9.2.2
  path_provider: ^2.1.4

  # Localization
  intl: ^0.20.2

  # Image Handling
  cached_network_image: ^3.4.1
  flutter_cache_manager: ^3.4.1
  image_picker: ^1.1.2
  flutter_image_compress: ^2.3.0

  # Logging
  logger: ^2.4.0

  # Firebase (temporarily disabled)
  # firebase_core: ^3.8.0
  # firebase_crashlytics: ^4.1.8
  # firebase_analytics: ^11.3.8

  # Animations
  lottie: ^3.2.0

  # JSON Serialization
  json_annotation: ^4.9.0

  # Functional Programming
  dartz: ^0.10.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.13
  flutter_gen_runner: ^5.8.0
  json_serializable: ^6.8.0
  freezed: ^2.5.7

  # App Configuration
  flutter_native_splash: ^2.4.1
  flutter_launcher_icons: ^0.14.1

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/lottie/
    - assets/lottie/

  # fonts:
  #   - family: Inter
  #     fonts:
  #       - asset: assets/fonts/Inter-Regular.ttf
  #       - asset: assets/fonts/Inter-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Inter-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Inter-Bold.ttf
  #         weight: 700

# Flutter Gen Configuration
flutter_gen:
  output: lib/core/gen/
  line_length: 80
  integrations:
    flutter_svg: true
    lottie: true

# Native Splash Configuration
flutter_native_splash:
  color: "#1a1a1a"
  image: assets/images/sinflix_splash.png
  android_12:
    image: assets/images/sinflix_splash.png
    color: "#1a1a1a"

# Launcher Icons Configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/sinflix_logo.png"
  min_sdk_android: 21
  # Android adaptive icon
  adaptive_icon_background: "#1a1a1a"
  adaptive_icon_foreground: "assets/images/sinflix_logo.png"
  # Remove old launcher icons
  remove_alpha_ios: true
